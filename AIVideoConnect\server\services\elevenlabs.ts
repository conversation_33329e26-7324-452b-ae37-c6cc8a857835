export async function generateElevenLabsTTS(text: string, voiceId: string = "efD2roF1M5pMoNxfHprk"): Promise<Buffer> {
  try {
    console.log(`🎤 Generating ElevenLabs TTS for: "${text.substring(0, 50)}..."`);

    const apiKey = process.env.ELEVENLABS_API_KEY;
    if (!apiKey || apiKey === "your_elevenlabs_api_key_here") {
      throw new Error("ElevenLabs API key not configured. Please set ELEVENLABS_API_KEY in your .env file.");
    }

    const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': apiKey
      },
      body: JSON.stringify({
        text: text,
        model_id: "eleven_multilingual_v2",
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.8,
          style: 0.2,
          use_speaker_boost: true
        }
      })
    });

    if (!response.ok) {
      throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
    }

    const audioBuffer = Buffer.from(await response.arrayBuffer());
    console.log(`✅ ElevenLabs TTS generated: ${audioBuffer.length} bytes`);
    return audioBuffer;

  } catch (error: any) {
    console.error('❌ ElevenLabs TTS error:', error);
    throw new Error(`ElevenLabs TTS failed: ${error.message}`);
  }
}

export async function getAvailableVoices() {
  try {
    const apiKey = process.env.ELEVENLABS_API_KEY;
    if (!apiKey || apiKey === "your_elevenlabs_api_key_here") {
      throw new Error("ElevenLabs API key not configured.");
    }

    const response = await fetch('https://api.elevenlabs.io/v1/voices', {
      headers: {
        'xi-api-key': apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`ElevenLabs API error: ${response.status}`);
    }

    const data = await response.json();
    return data.voices;
  } catch (error: any) {
    console.error('❌ Failed to get ElevenLabs voices:', error);
    throw new Error(`Failed to get voices: ${error.message}`);
  }
}

// Voice ID for AI Trainer - using "Clyde" (intense, middle-aged male, great for fitness training)
export const AI_TRAINER_VOICE_ID = "2EiwWnXFnvU5JabPnv8n";

// Enhanced TTS function specifically for fitness coaching with Clyde's personality
export async function generateFitnessCoachTTS(text: string): Promise<Buffer> {
  return generateElevenLabsTTS(text, AI_TRAINER_VOICE_ID);
}
