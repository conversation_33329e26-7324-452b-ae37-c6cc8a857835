import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import <PERSON><PERSON> from "stripe";
// Removed storage and assessment service imports for simplified conversation mode
import { generateFitnessCoachResponse, transcribeAudio } from "./services/openai";
import { generateElevenLabsTTS, AI_TRAINER_VOICE_ID } from "./services/elevenlabs";
// Removed characterAI import for simplified conversation mode
// Removed schema import for simplified conversation mode

// Extend WebSocket interface to include sessionId and conversation history
interface ExtendedWebSocket extends WebSocket {
  sessionId?: string;
  conversationHistory?: string[];
}

// Initialize Stripe only if secret key is available
const stripe = process.env.STRIPE_SECRET_KEY 
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: "2025-08-27.basil",
    })
  : null;

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // WebSocket server for real-time communication
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });
  
  wss.on('connection', (ws) => {
    console.log('✅ WebSocket client connected');
    
    ws.on('message', async (message) => {
      try {
        const data = JSON.parse(message.toString());
        
        switch (data.type) {
          case 'join_session':
            ws.sessionId = data.sessionId;
            ws.conversationHistory = [];
            ws.send(JSON.stringify({ type: 'session_joined', sessionId: data.sessionId }));
            break;
            
          case 'speech_data':
            // Handle speech-to-text data and generate AI response
            if (ws.sessionId) {
              console.log(`🎙️ Received speech data for session ${ws.sessionId}: "${data.transcript}"`);

              // Initialize conversation history if not exists
              if (!ws.conversationHistory) {
                ws.conversationHistory = [];
              }

              // Add user message to conversation history
              ws.conversationHistory.push(data.transcript);

              // Generate AI response using fitness coach system
              try {
                const aiResponse = await generateFitnessCoachResponse(data.transcript, ws.conversationHistory);

                // Add AI response to conversation history
                ws.conversationHistory.push(aiResponse);

                // Keep conversation history manageable (last 20 messages)
                if (ws.conversationHistory.length > 20) {
                  ws.conversationHistory = ws.conversationHistory.slice(-20);
                }

                // Send AI response back to the client
                ws.send(JSON.stringify({
                  type: 'ai_response',
                  message: aiResponse
                }));

                console.log(`Clyde responded:`, aiResponse);
              } catch (error) {
                console.error('Failed to generate fitness coach response:', error);
                // Send fallback response
                ws.send(JSON.stringify({
                  type: 'ai_response',
                  message: "Hey, I'm here to help you with your fitness goals. What's on your mind?"
                }));
              }
              
              // Broadcast to other clients in session
              wss.clients.forEach(client => {
                if (client !== ws && client.readyState === WebSocket.OPEN && client.sessionId === ws.sessionId) {
                  client.send(JSON.stringify({ type: 'speech_update', data: data }));
                }
              });
            }
            break;
            
          case 'pose_data':
            // Handle pose detection data - no storage needed for simplified mode
            console.log('📊 Pose data received for session:', ws.sessionId);
            break;
            
          case 'whisper_audio':
            // Handle Whisper audio transcription
            if (ws.sessionId && data.audio) {
              try {
                console.log(`🎙️ Processing Whisper audio for session ${ws.sessionId}`);

                // Decode base64 audio
                const audioBuffer = Buffer.from(data.audio, 'base64');
                console.log(`📦 Audio buffer size: ${audioBuffer.length} bytes`);

                // Transcribe with Whisper using provided MIME type
                const mimeType = data.mimeType || 'audio/webm';
                const language = data.language || 'en';

                const transcription = await transcribeAudio(audioBuffer, language, mimeType);
                console.log(`✅ Whisper transcription: "${transcription.text}" (confidence: ${Math.round((transcription.confidence || 0.8) * 100)}%)`);

                // No database storage needed for simplified conversation mode

                // Initialize conversation history if not exists
                if (!ws.conversationHistory) {
                  ws.conversationHistory = [];
                }

                // Add user message to conversation history
                ws.conversationHistory.push(transcription.text);

                // Generate AI response using fitness coach system
                const aiResponse = await generateFitnessCoachResponse(transcription.text, ws.conversationHistory);

                // Add AI response to conversation history
                ws.conversationHistory.push(aiResponse);

                // Keep conversation history manageable (last 20 messages)
                if (ws.conversationHistory.length > 20) {
                  ws.conversationHistory = ws.conversationHistory.slice(-20);
                }

                // Send AI response back to the client with detailed transcription info
                ws.send(JSON.stringify({
                  type: 'ai_response',
                  message: aiResponse,
                  transcript: transcription.text,
                  confidence: transcription.confidence,
                  duration: transcription.duration,
                  source: 'whisper'
                }));

                console.log(`🤖 Clyde Response sent: "${aiResponse}"`);
              } catch (error: any) {
                console.error('❌ Whisper transcription error:', error);

                // Send specific error message based on error type
                let errorMessage = "I'm having trouble hearing you. Could you try again?";
                let errorType = 'transcription_failed';

                if (error.message.includes('too short')) {
                  errorMessage = "Please speak for a bit longer. I need at least a few words to understand you.";
                  errorType = 'audio_too_short';
                } else if (error.message.includes('format')) {
                  errorMessage = "There's an issue with your audio format. Please check your microphone settings.";
                  errorType = 'audio_format_error';
                } else if (error.message.includes('quota') || error.message.includes('rate limit')) {
                  errorMessage = "I'm processing too many requests right now. Please wait a moment and try again.";
                  errorType = 'api_limit_error';
                }

                ws.send(JSON.stringify({
                  type: 'ai_response',
                  message: errorMessage,
                  error: errorType,
                  errorDetails: error.message
                }));
              }
            }
            break;

          case 'tts_audio':
            // Handle TTS audio analysis for orb animation
            if (ws.sessionId) {
              wss.clients.forEach(client => {
                if (client !== ws && client.readyState === WebSocket.OPEN && client.sessionId === ws.sessionId) {
                  client.send(JSON.stringify({ type: 'orb_animation', data: data.audioData }));
                }
              });
            }
            break;
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });
    
    ws.on('close', (code, reason) => {
      console.log(`🔌 WebSocket client disconnected (code: ${code}, reason: ${reason})`);
    });

    ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error);
    });
  });

  // Simple conversation session endpoint (no database needed)
  app.post("/api/conversation/start", async (req, res) => {
    try {
      const sessionId = `conversation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      res.json({
        id: sessionId,
        status: "active",
        message: "Conversation session started"
      });
    } catch (error: any) {
      res.status(500).json({ message: "Error starting conversation: " + error.message });
    }
  });

  // Removed assessment endpoint - using simplified conversation mode

  // All remaining assessment endpoints removed for simplified conversation mode

  // TTS endpoint using ElevenLabs with Clyde voice character
  app.post("/api/tts", async (req, res) => {
    try {
      const { text } = req.body;

      if (!text) {
        return res.status(400).json({ message: "Text is required" });
      }

      let audioBuffer: Buffer;

      try {
        // Use ElevenLabs with Clyde voice for fitness coaching
        console.log(`🎤 Using ElevenLabs (Clyde) for: "${text.substring(0, 50)}..."`);
        const { generateFitnessCoachTTS } = await import("./services/elevenlabs");
        audioBuffer = await generateFitnessCoachTTS(text);
      } catch (elevenLabsError: any) {
        // Fallback to OpenAI if ElevenLabs fails
        console.warn('⚠️ ElevenLabs failed, falling back to OpenAI:', elevenLabsError.message);
        const { generateTTS } = await import("./services/openai");
        audioBuffer = await generateTTS(text, "onyx");
      }

      res.set({
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.length,
        'Cache-Control': 'no-cache',
      });

      res.send(audioBuffer);
    } catch (error: any) {
      console.error('❌ TTS generation error:', error);
      res.status(500).json({ message: "Error generating TTS: " + error.message });
    }
  });

  // Character.AI endpoints removed for simplified conversation mode

  // Workout plan endpoints removed for simplified conversation mode

  // User plans endpoint removed for simplified conversation mode

  // Stripe subscription endpoint removed for simplified conversation mode

  // Health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      services: {
        websocket: "active",
        tts: "available"
      }
    });
  });

  return httpServer;
}
