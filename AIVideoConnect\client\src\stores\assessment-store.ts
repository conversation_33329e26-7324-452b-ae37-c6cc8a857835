import { create } from 'zustand';

interface ConversationMessage {
  role: 'user' | 'assistant';
  message: string;
  timestamp: Date;
}

interface ConversationState {
  sessionId: string | null;
  currentPrompt: string;
  conversationHistory: ConversationMessage[];
  isListening: boolean;
  isSpeaking: boolean;

  setSessionId: (id: string) => void;
  setCurrentPrompt: (prompt: string) => void;
  addMessage: (role: 'user' | 'assistant', message: string) => void;
  setListening: (listening: boolean) => void;
  setSpeaking: (speaking: boolean) => void;
  reset: () => void;
}

export const useConversationStore = create<ConversationState>((set) => ({
  sessionId: null,
  currentPrompt: "Hey there! I'm <PERSON>, your AI fitness coach. What fitness goals are you working on?",
  conversationHistory: [],
  isListening: false,
  isSpeaking: false,

  setSessionId: (id) => set({ sessionId: id }),
  setCurrentPrompt: (prompt) => set({ currentPrompt: prompt }),
  addMessage: (role, message) => set((state) => ({
    conversationHistory: [
      ...state.conversationHistory,
      { role, message, timestamp: new Date() }
    ]
  })),
  setListening: (listening) => set({ isListening: listening }),
  setSpeaking: (speaking) => set({ isSpeaking: speaking }),
  reset: () => set({
    sessionId: null,
    currentPrompt: "Hey there! I'm Clyde, your AI fitness coach. What fitness goals are you working on?",
    conversationHistory: [],
    isListening: false,
    isSpeaking: false,
  }),
}));

// Keep the old export for backward compatibility during transition
export const useAssessmentStore = useConversationStore;
