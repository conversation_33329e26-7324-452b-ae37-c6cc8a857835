import { useEffect, useState, useRef } from "react";
import OrbAvatar from "@/components/orb-avatar";
import { useSpeech } from "@/hooks/use-speech";
import { useWebSocket } from "@/hooks/use-websocket";
import { useTTS } from "@/hooks/use-tts";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Mic, MicOff, Volume2, VolumeX } from "lucide-react";

export default function Conversation() {
  const [currentSessionId, setCurrentSessionId] = useState<string>(`session-${Date.now()}`);
  const [currentPrompt, setCurrentPrompt] = useState<string>("Hey there! I'm <PERSON>, your AI fitness coach. What fitness goals are you working on?");
  const [conversationHistory, setConversationHistory] = useState<Array<{role: 'user' | 'assistant', message: string}>>([]);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [orbState, setOrbState] = useState<"idle" | "listening" | "speaking">("idle");

  const websocket = useWebSocket();
  const {
    isListening,
    transcript,
    error: speechError,
    startListening,
    stopListening,
  } = useSpeech(websocket);
  const { speak, isPlaying, error: ttsError } = useTTS();

  // Connect to WebSocket when component mounts
  useEffect(() => {
    if (currentSessionId) {
      websocket.connect(currentSessionId);
    }
    return () => {
      websocket.disconnect();
    };
  }, [currentSessionId, websocket]);

  // Subscribe to AI responses
  useEffect(() => {
    const unsubscribe = websocket.subscribe('ai_response', (data) => {
      console.log('Received AI response:', data.message);
      setCurrentPrompt(data.message);
      
      // Add to conversation history
      setConversationHistory(prev => [
        ...prev,
        { role: 'assistant', message: data.message }
      ]);
      
      // Speak the AI response if audio is enabled
      if (isAudioEnabled) {
        speak(data.message);
      }
    });

    return unsubscribe;
  }, [websocket, speak, isAudioEnabled]);

  // Handle transcript updates
  useEffect(() => {
    if (transcript) {
      // Add user message to conversation history
      setConversationHistory(prev => [
        ...prev,
        { role: 'user', message: transcript }
      ]);
    }
  }, [transcript]);

  // Update orb state based on listening and speaking
  useEffect(() => {
    if (isListening) {
      setOrbState("listening");
    } else if (isPlaying) {
      setOrbState("speaking");
    } else {
      setOrbState("idle");
    }
  }, [isListening, isPlaying]);

  // Speak initial greeting when component mounts
  useEffect(() => {
    if (isAudioEnabled && currentPrompt && conversationHistory.length === 0) {
      speak(currentPrompt);
    }
  }, [isAudioEnabled, currentPrompt, speak, conversationHistory.length]);

  const handleMicToggle = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  const handleAudioToggle = () => {
    setIsAudioEnabled(!isAudioEnabled);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">
            AI Fitness Coach
          </h1>
          <p className="text-muted-foreground">
            Chat with Clyde, your personal fitness trainer
          </p>
        </div>

        {/* Main Conversation Interface */}
        <div className="grid lg:grid-cols-2 gap-6 mb-6">
          {/* AI Trainer Orb */}
          <Card className="bg-card rounded-2xl border border-border overflow-hidden aspect-video relative">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10"></div>
            
            {/* Holographic Orb Avatar */}
            <div className="absolute inset-0 flex items-center justify-center">
              <OrbAvatar
                state={orbState}
                engagement={0.8}
                intensity={0.6}
                className="scale-75 lg:scale-100"
                data-testid="ai-orb"
              />
            </div>

            {/* Status Indicator */}
            <div className="absolute top-4 left-4">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                orbState === 'listening' ? 'bg-green-500/20 text-green-400' :
                orbState === 'speaking' ? 'bg-blue-500/20 text-blue-400' :
                'bg-gray-500/20 text-gray-400'
              }`}>
                {orbState === 'listening' ? 'Listening...' :
                 orbState === 'speaking' ? 'Speaking...' :
                 'Ready to chat'}
              </div>
            </div>

            {/* Current Prompt Display */}
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
                <p className="text-white text-sm leading-relaxed">
                  {currentPrompt}
                </p>
              </div>
            </div>
          </Card>

          {/* Conversation History */}
          <Card className="bg-card rounded-2xl border border-border overflow-hidden">
            <CardContent className="p-6 h-full flex flex-col">
              <h3 className="text-lg font-semibold mb-4">Conversation</h3>
              
              <div className="flex-1 overflow-y-auto space-y-3 mb-4">
                {conversationHistory.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <p>Start a conversation by clicking the microphone button</p>
                  </div>
                ) : (
                  conversationHistory.map((entry, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg ${
                        entry.role === 'user'
                          ? 'bg-primary/10 ml-8'
                          : 'bg-secondary/10 mr-8'
                      }`}
                    >
                      <div className="text-xs text-muted-foreground mb-1">
                        {entry.role === 'user' ? 'You' : 'Clyde'}
                      </div>
                      <p className="text-sm">{entry.message}</p>
                    </div>
                  ))
                )}
              </div>

              {/* Current Transcript */}
              {transcript && (
                <div className="bg-primary/5 border border-primary/20 rounded-lg p-3 mb-4">
                  <div className="text-xs text-primary mb-1">You (speaking...)</div>
                  <p className="text-sm text-primary">{transcript}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Controls */}
        <div className="flex justify-center gap-4">
          <Button
            onClick={handleMicToggle}
            size="lg"
            variant={isListening ? "destructive" : "default"}
            className="rounded-full w-16 h-16"
          >
            {isListening ? <MicOff className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
          </Button>

          <Button
            onClick={handleAudioToggle}
            size="lg"
            variant={isAudioEnabled ? "default" : "outline"}
            className="rounded-full w-16 h-16"
          >
            {isAudioEnabled ? <Volume2 className="w-6 h-6" /> : <VolumeX className="w-6 h-6" />}
          </Button>
        </div>

        {/* Error Messages */}
        {(speechError || ttsError) && (
          <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-destructive text-sm">
              {speechError || ttsError}
            </p>
          </div>
        )}

        {/* Connection Status */}
        <div className="mt-4 text-center">
          <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs ${
            websocket.isConnected 
              ? 'bg-green-500/20 text-green-400' 
              : 'bg-red-500/20 text-red-400'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              websocket.isConnected ? 'bg-green-400' : 'bg-red-400'
            }`} />
            {websocket.isConnected ? 'Connected' : 'Disconnected'}
          </div>
        </div>
      </div>
    </div>
  );
}
