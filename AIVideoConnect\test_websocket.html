<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test - Clyde AI Fitness Coach</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .messages { border: 1px solid #ccc; height: 400px; overflow-y: scroll; padding: 10px; margin: 10px 0; }
        .message { margin: 5px 0; padding: 5px; border-radius: 5px; }
        .user { background-color: #e3f2fd; }
        .assistant { background-color: #f3e5f5; }
        input[type="text"] { width: 70%; padding: 10px; }
        button { padding: 10px 20px; margin: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #c8e6c9; }
        .disconnected { background-color: #ffcdd2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏋️ Clyde AI Fitness Coach - WebSocket Test</h1>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div id="messages" class="messages"></div>
        
        <div>
            <input type="text" id="messageInput" placeholder="Ask <PERSON> about fitness..." />
            <button onclick="sendMessage()">Send Message</button>
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>
        
        <div>
            <h3>Quick Test Messages:</h3>
            <button onclick="sendQuickMessage('Hey Clyde, I want to start working out but I\'m a complete beginner')">Beginner Question</button>
            <button onclick="sendQuickMessage('What\'s the best way to build muscle?')">Muscle Building</button>
            <button onclick="sendQuickMessage('I have lower back pain, what should I avoid?')">Injury Prevention</button>
        </div>
    </div>

    <script>
        let ws = null;
        let sessionId = null;

        function addMessage(sender, message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.innerHTML = `<strong>${sender === 'user' ? 'You' : 'Clyde'}:</strong> ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(status, isConnected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${isConnected ? 'connected' : 'disconnected'}`;
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('system', 'Already connected');
                return;
            }

            // Generate a session ID
            sessionId = `test-session-${Date.now()}`;
            
            // Connect to WebSocket
            ws = new WebSocket('ws://localhost:3000/ws');
            
            ws.onopen = function() {
                updateStatus('Connected', true);
                addMessage('system', 'Connected to Clyde AI Fitness Coach');
                
                // Join session
                ws.send(JSON.stringify({
                    type: 'join_session',
                    sessionId: sessionId
                }));
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                console.log('Received:', data);
                
                if (data.type === 'ai_response') {
                    addMessage('assistant', data.message);
                } else if (data.type === 'session_joined') {
                    addMessage('system', `Joined session: ${data.sessionId}`);
                }
            };
            
            ws.onclose = function() {
                updateStatus('Disconnected', false);
                addMessage('system', 'Disconnected from server');
            };
            
            ws.onerror = function(error) {
                updateStatus('Error', false);
                addMessage('system', 'Connection error: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('system', 'Not connected. Please connect first.');
                return;
            }
            
            addMessage('user', message);
            
            // Send as speech data (simulating speech recognition)
            ws.send(JSON.stringify({
                type: 'speech_data',
                transcript: message,
                confidence: 0.95
            }));
            
            input.value = '';
        }

        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Auto-connect on page load
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html>
