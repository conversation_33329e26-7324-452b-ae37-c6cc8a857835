import { useRef, useCallback, useEffect, useState } from 'react';

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface WebSocketHookReturn {
  isConnected: boolean;
  sendMessage: (message: WebSocketMessage) => void;
  subscribe: (messageType: string, handler: (data: any) => void) => () => void;
  connect: (sessionId: string) => void;
  disconnect: () => void;
}

export function useWebSocket(): WebSocketHookReturn {
  const wsRef = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const handlersRef = useRef<Map<string, Set<(data: any) => void>>>(new Map());
  const sessionIdRef = useRef<string | null>(null);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message:', message);
    }
  }, []);

  const subscribe = useCallback((messageType: string, handler: (data: any) => void) => {
    if (!handlersRef.current.has(messageType)) {
      handlersRef.current.set(messageType, new Set());
    }
    handlersRef.current.get(messageType)!.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = handlersRef.current.get(messageType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          handlersRef.current.delete(messageType);
        }
      }
    };
  }, []);

  const connect = useCallback((sessionId: string) => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.warn('WebSocket connection attempted in non-browser environment');
      return;
    }

    // Close existing connection if any
    if (wsRef.current && wsRef.current.readyState !== WebSocket.CLOSED) {
      console.log('Closing existing WebSocket connection');
      wsRef.current.close();
    }

    sessionIdRef.current = sessionId;
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const host = window.location.host || 'localhost:3000';
    const wsUrl = `${protocol}//${host}/ws`;

    console.log('Centralized WebSocket connecting to:', wsUrl);
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      console.log('Centralized WebSocket connected');
      setIsConnected(true);
      // Join the session
      ws.send(JSON.stringify({ type: 'join_session', sessionId }));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('WebSocket message received:', data.type);

        // Dispatch message to all registered handlers for this type
        const handlers = handlersRef.current.get(data.type);
        if (handlers) {
          handlers.forEach(handler => {
            try {
              handler(data);
            } catch (error) {
              console.error('Error in WebSocket message handler:', error);
            }
          });
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('Centralized WebSocket error:', error);
      setIsConnected(false);
    };

    ws.onclose = (event) => {
      console.log('Centralized WebSocket closed:', event.code, event.reason);
      setIsConnected(false);

      // Only attempt reconnection for unexpected closures and if we still have the same session
      if (event.code !== 1000 && event.code !== 1001 && sessionIdRef.current && wsRef.current === ws) {
        console.log('Scheduling WebSocket reconnection in 2 seconds...');
        setTimeout(() => {
          // Only reconnect if we still have the same session and no new connection was made
          if (sessionIdRef.current && (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED)) {
            console.log('Attempting WebSocket reconnection...');
            connect(sessionIdRef.current);
          }
        }, 2000);
      }
    };
  }, []);

  const disconnect = useCallback(() => {
    console.log('Disconnecting centralized WebSocket');
    if (wsRef.current && wsRef.current.readyState !== WebSocket.CLOSED) {
      wsRef.current.close();
    }
    wsRef.current = null;
    sessionIdRef.current = null;
    setIsConnected(false);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    sendMessage,
    subscribe,
    connect,
    disconnect,
  };
}
