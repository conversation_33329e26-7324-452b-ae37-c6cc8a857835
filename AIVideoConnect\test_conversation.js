// Simple test to verify the conversation flow works
import WebSocket from 'ws';

async function testConversation() {
  console.log('🧪 Testing End-to-End Conversation Flow...\n');
  
  return new Promise((resolve, reject) => {
    const ws = new WebSocket('ws://localhost:3000/ws');
    let responseReceived = false;
    
    ws.on('open', function open() {
      console.log('✅ WebSocket connected');
      
      // Join a session
      const sessionId = `test-${Date.now()}`;
      ws.send(JSON.stringify({
        type: 'join_session',
        sessionId: sessionId
      }));
      
      // Wait a moment then send a test message
      setTimeout(() => {
        console.log('📤 Sending test message: "Hey <PERSON>, what\'s the best exercise for beginners?"');
        ws.send(JSON.stringify({
          type: 'speech_data',
          transcript: "Hey <PERSON>, what's the best exercise for beginners?",
          confidence: 0.95
        }));
      }, 1000);
    });
    
    ws.on('message', function message(data) {
      const response = JSON.parse(data.toString());
      console.log('📥 Received:', response.type);
      
      if (response.type === 'ai_response') {
        console.log('🤖 <PERSON> responded:', response.message);
        responseReceived = true;
        ws.close();
        resolve(true);
      }
    });
    
    ws.on('close', function close() {
      console.log('🔌 WebSocket disconnected');
      if (!responseReceived) {
        reject(new Error('No AI response received'));
      }
    });
    
    ws.on('error', function error(err) {
      console.error('❌ WebSocket error:', err);
      reject(err);
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!responseReceived) {
        ws.close();
        reject(new Error('Test timeout - no response received'));
      }
    }, 30000);
  });
}

// Run the test
testConversation()
  .then(() => {
    console.log('\n✅ End-to-End Conversation Test PASSED!');
    console.log('🎉 Your AI Fitness Trainer with Clyde is working perfectly!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ End-to-End Conversation Test FAILED:', error.message);
    process.exit(1);
  });
