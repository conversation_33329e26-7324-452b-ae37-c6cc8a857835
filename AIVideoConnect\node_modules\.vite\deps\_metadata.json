{"hash": "823ff8b5", "configHash": "e9789ae3", "lockfileHash": "60e0cc5c", "browserHash": "763a0a06", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "0c3a1588", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "4b05c2ea", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ac18f15e", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "69fd609c", "needsInterop": true}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "50fc2887", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "0bbe70ca", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "8031d19a", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c9670b80", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "1405ceea", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "46e13456", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "117a658e", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "011305d8", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "6f3f7014", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-5TIKPGMF": {"file": "chunk-5TIKPGMF.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}